<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>百度AI - 智能创造无限可能</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 15px 0;
            position: fixed;
            width: 100%;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            display: flex;
            align-items: center;
            font-size: 24px;
            font-weight: bold;
            color: #3385ff;
        }

        .logo::before {
            content: "🤖";
            margin-right: 10px;
            font-size: 28px;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 30px;
        }

        .nav-links a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-links a:hover {
            color: #3385ff;
        }

        .main-content {
            padding-top: 100px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .hero-section {
            max-width: 800px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            animation: fadeInUp 1s ease-out;
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255, 255, 255, 0.9);
            margin-bottom: 40px;
            line-height: 1.6;
            animation: fadeInUp 1s ease-out 0.2s both;
        }

        .search-container {
            background: white;
            border-radius: 50px;
            padding: 8px;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
            margin-bottom: 40px;
            max-width: 600px;
            width: 100%;
            animation: fadeInUp 1s ease-out 0.4s both;
        }

        .search-box {
            display: flex;
            align-items: center;
        }

        .search-input {
            flex: 1;
            border: none;
            outline: none;
            padding: 15px 25px;
            font-size: 16px;
            border-radius: 50px;
            background: transparent;
        }

        .search-btn {
            background: linear-gradient(45deg, #3385ff, #4d94ff);
            border: none;
            border-radius: 50px;
            padding: 15px 30px;
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(51, 133, 255, 0.4);
        }

        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-top: 60px;
            max-width: 1000px;
            width: 100%;
            padding: 0 20px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            animation: fadeInUp 1s ease-out 0.6s both;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            display: block;
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .feature-desc {
            color: #666;
            line-height: 1.6;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }

            .nav-links {
                display: none;
            }

            .features {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="logo">百度AI</div>
            <ul class="nav-links">
                <li><a href="#home">首页</a></li>
                <li><a href="#products">产品</a></li>
                <li><a href="#solutions">解决方案</a></li>
                <li><a href="#developers">开发者</a></li>
                <li><a href="#about">关于我们</a></li>
            </ul>
        </nav>
    </header>

    <main class="main-content">
        <section class="hero-section">
            <h1 class="hero-title">智能创造无限可能</h1>
            <p class="hero-subtitle">
                百度AI致力于让复杂的世界更简单，用人工智能技术赋能各行各业，
                <br>让每个人都能享受AI带来的便利与创新
            </p>

            <div class="search-container">
                <div class="search-box">
                    <input type="text" class="search-input" placeholder="体验百度AI，输入您想了解的内容...">
                    <button class="search-btn">智能搜索</button>
                </div>
            </div>

            <div class="features">
                <div class="feature-card">
                    <span class="feature-icon">🧠</span>
                    <h3 class="feature-title">文心一言</h3>
                    <p class="feature-desc">基于飞桨深度学习平台和文心大模型，提供智能对话、内容创作等服务</p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">👁️</span>
                    <h3 class="feature-title">计算机视觉</h3>
                    <p class="feature-desc">图像识别、人脸识别、OCR文字识别等视觉AI技术，让机器看懂世界</p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🎤</span>
                    <h3 class="feature-title">语音技术</h3>
                    <p class="feature-desc">语音识别、语音合成、声纹识别，让人机交互更自然</p>
                </div>

                <div class="feature-card">
                    <span class="feature-icon">🚗</span>
                    <h3 class="feature-title">自动驾驶</h3>
                    <p class="feature-desc">Apollo自动驾驶开放平台，引领智能出行新时代</p>
                </div>
            </div>
        </section>
    </main>

    <script>
        // 搜索功能
        document.querySelector('.search-btn').addEventListener('click', function() {
            const searchInput = document.querySelector('.search-input');
            const query = searchInput.value.trim();

            if (query) {
                // 这里可以添加实际的搜索逻辑
                alert(`正在为您搜索: "${query}"`);
                searchInput.value = '';
            } else {
                alert('请输入搜索内容');
            }
        });

        // 回车搜索
        document.querySelector('.search-input').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.querySelector('.search-btn').click();
            }
        });

        // 平滑滚动
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });

        // 添加一些交互效果
        document.querySelectorAll('.feature-card').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    </script>
</body>
</html>